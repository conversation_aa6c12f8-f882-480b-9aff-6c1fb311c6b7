.layout {
  @apply flex h-full min-h-screen flex-col;
}

.sidebar {
  @apply fixed bottom-0 left-0 top-0 flex h-full w-80 bg-white shadow-medium transition-all dark:bg-background;
}

.content {
  @apply flex flex-1 flex-col ps-80;
}

.heading {
  @apply sticky top-0 z-50 flex w-full px-5 pt-5;

  &::after {
    @apply absolute bottom-0 left-0 right-0 top-0 -z-10 bg-gradient-to-b from-primary-600/20 from-50% to-primary-100/0 backdrop-blur-md content-empty [mask:linear-gradient(hsl(var(--heroui-background)),hsl(var(--heroui-background))_50%,transparent_100%)] dark:from-background/20;
  }

  &-navbar {
    @apply z-70 rounded-medium bg-background shadow-medium dark:bg-background;

    &__wrapper {
      @apply max-w-full pe-4.5 ps-3;
    }

    &__content {
      @apply gap-2;
    }
  }
}

.main {
  @apply flex flex-1 flex-col items-start justify-start gap-5 p-5;

  &-content {
    @apply flex w-full flex-col justify-between gap-5 align-top;
  }
}

.sidebar-trigger {
  &__icon {
    @apply h-6 w-6;
  }
}
